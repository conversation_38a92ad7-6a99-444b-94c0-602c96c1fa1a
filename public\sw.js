if (!self.define) {
  let e,
    s = {};
  const t = (t, a) => (
    (t = new URL(t + '.js', a).href),
    s[t] ||
      new Promise((s) => {
        if ('document' in self) {
          const e = document.createElement('script');
          (e.src = t), (e.onload = s), document.head.appendChild(e);
        } else (e = t), importScripts(t), s();
      }).then(() => {
        let e = s[t];
        if (!e) throw new Error(`Module ${t} didn’t register its module`);
        return e;
      })
  );
  self.define = (a, n) => {
    const i =
      e ||
      ('document' in self ? document.currentScript.src : '') ||
      location.href;
    if (s[i]) return;
    let c = {};
    const d = (e) => t(e, i),
      r = { module: { uri: i }, exports: c, require: d };
    s[i] = Promise.all(a.map((e) => r[e] || d(e))).then((e) => (n(...e), c));
  };
}
define(['./workbox-1bb06f5e'], function (e) {
  'use strict';
  importScripts(),
    self.skipWaiting(),
    e.clientsClaim(),
    e.precacheAndRoute(
      [
        {
          url: '/_next/app-build-manifest.json',
          revision: '0420a6acb3748a950eae210d2964fa74',
        },
        {
          url: '/_next/static/8sS8dtbkPMRL2UHxNMhNl/_buildManifest.js',
          revision: '0f825844dd8ffe95c1830472983ff2e5',
        },
        {
          url: '/_next/static/8sS8dtbkPMRL2UHxNMhNl/_ssgManifest.js',
          revision: 'b6652df95db52feb4daf4eca35380933',
        },
        {
          url: '/_next/static/chunks/1044-989d46e3553a5261.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/1078-5dc9f867a616958f.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/1684-2024d14f0d42f258.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/186-8d1910733644a6fb.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/1887-04d958d6754f21f1.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/2289-6ad31711bb1c6d62.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/2441-761faccc465dbbef.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/259-3ae38508df362274.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/278-34dc95bef7076e24.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/2950-469b40ec3a61b8c1.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/2982-423d748aae90a6e0.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/3464-17e00d9ba44f28f7.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/3605-d01460ee32fa5c6f.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/428-4490300c62dadb3c.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/472.2c08b965bd9148e2.js',
          revision: '2c08b965bd9148e2',
        },
        {
          url: '/_next/static/chunks/4792-304b1194526a9114.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/4945-4d0ef61a214c9d71.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/4bd1b696-9d7633aa5bd4fe4f.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/5157-3549aa120e199458.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/5521-171c5b9d659d6c15.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/5766-baba0607c813113c.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/6069-ead01464727496cf.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/6240-ff26481c6c4af279.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/6671-e6da9dd8068cdc87.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/6730-9271a3aac212ff03.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/6766-afa4461e8652e50a.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/6874-49b8f86bf7767823.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/7031-d006246bb6dade6a.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/7039-049ca1c3ee818c79.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/7096-7707ecd4cdd8e202.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/8234-ff10455b1383a414.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/8469-fc65f7730383acab.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/8617-f9054e3ce89dc253.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/8746-87cce52c3c09df9c.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/879-a20ba80222509cab.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/9269-ba7f4bbca2c7856a.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/9293-790b0f29255542ae.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/9341.c00e1bbc955787be.js',
          revision: 'c00e1bbc955787be',
        },
        {
          url: '/_next/static/chunks/9388-6169167ec77d674c.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/9481-ce68daa3d6d4cfdb.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/9519-918d92a362862377.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/9638-8cd796d7ab183919.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/9669-1366d587a70e85ba.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/998-f38b4aef2b28a380.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/cafeteria/page-0c003d93eb276029.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/dashboard/page-710b2822030f7317.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/data-analysis/page-2e1bad67c7641d6c.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/discounts/discount-code/page-182a24862cb7d0b6.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/discounts/page-da5e9d3fe25c2a8b.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/feedbacks/page-10acb89281bfc2a2.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/forum/direct-messages/page-648014c7f4ad9cd2.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/forum/groups/%5BgroupId%5D/page-05870e97eaf31802.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/forum/page-4811fea5403edf9a.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/incident-reporting/page-140593da776eb55e.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/layout-de9fe47b58eb8745.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/locations/page-a3809b819d544b61.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/manage-staffs/page-a0559a1ec79a29d9.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/packages/edit-package/%5Bslug%5D/page-0c55fb1322246fa9.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/packages/investigation/page-78e73e4a7b3603a6.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/packages/manage-category/page-074f22c6c86cf299.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/packages/manage-package/page-376543da85c2d890.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/packages/new-package/page-08531d52e97c3f2b.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/packages/page-ab5cb63a4dd79226.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/analytics/page-ad7591e622c47442.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/appointments/page-f4cc29ce32869626.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/departments/page-eb1a2ec609ce243f.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/doctors/page-35122a18c5ba67e1.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/interactions/page-58903845299c7c6d.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/medical-records/page-001d57d87b92ee77.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/page-f5e6dd161afa4b3d.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/patients/page-e6401291b503effa.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/referrals/page-9fda036adab576c3.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/referrals/referring-entities/page-90188a2ca039cc9b.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/referrals/rewards/page-3e6164c0ef6cd0e6.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/rewards/page-473158b706e037e0.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/system-settings/page-a3973905a45809a0.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/transactions/page-35ac88ff0a7bc7af.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/_not-found/page-72fa00e5a996b855.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/error-6492789314180dea.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/forbidden/page-c8ecdc5976d4f32a.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/layout-5571dfe0c0febb6a.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/login/page-5f8f2bfc75c4eae0.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/app/page-73c79a8d27ba3dbe.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/framework-c054b661e612b06c.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/main-app-15ab57bde25404f0.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/main-bf16eb4740561336.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/pages/_app-4e5feef8afe3c684.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/pages/_error-faae17b7f45ea4ac.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/chunks/polyfills-42372ed130431b0a.js',
          revision: '846118c33b2c0e922d7b3a7676f81f6f',
        },
        {
          url: '/_next/static/chunks/webpack-585805a6031f1a0a.js',
          revision: '8sS8dtbkPMRL2UHxNMhNl',
        },
        {
          url: '/_next/static/css/e056e5724079ac26.css',
          revision: 'e056e5724079ac26',
        },
        {
          url: '/_next/static/media/034d78ad42e9620c-s.woff2',
          revision: 'be7c930fceb794521be0a68e113a71d8',
        },
        {
          url: '/_next/static/media/29a4aea02fdee119-s.woff2',
          revision: '69d9d2cdadeab7225297d50fc8e48e8b',
        },
        {
          url: '/_next/static/media/4c285fdca692ea22-s.p.woff2',
          revision: '42d3308e3aca8742731f63154187bdd7',
        },
        {
          url: '/_next/static/media/6c177e25b87fd9cd-s.woff2',
          revision: '4f9434d4845212443bbd9d102f1f5d7d',
        },
        {
          url: '/_next/static/media/6c9a125e97d835e1-s.woff2',
          revision: '889718d692d5bfc6019cbdfcb5cc106f',
        },
        {
          url: '/_next/static/media/a1386beebedccca4-s.woff2',
          revision: 'd3aa06d13d3cf9c0558927051f3cb948',
        },
        {
          url: '/_next/static/media/b957ea75a84b6ea7-s.p.woff2',
          revision: '0bd523f6049956faaf43c254a719d06a',
        },
        {
          url: '/_next/static/media/eafabf029ad39a43-s.p.woff2',
          revision: '43751174b6b810eb169101a20d8c26f8',
        },
        {
          url: '/_next/static/media/fe0777f1195381cb-s.woff2',
          revision: 'f2a04185547c36abfa589651236a9849',
        },
        {
          url: '/_next/static/media/icon.0c4b6864.png',
          revision: '6c32b251eefc1a2fe5730ea8f1cda7fb',
        },
        {
          url: '/_next/static/media/logo.7bb171e6.png',
          revision: 'c4ff5bcbd8371f70a6b4dee2b692f68c',
        },
        { url: '/icon-512.png', revision: '6c32b251eefc1a2fe5730ea8f1cda7fb' },
        { url: '/icon.png', revision: '6c32b251eefc1a2fe5730ea8f1cda7fb' },
        { url: '/manifest.json', revision: '506f7804edbf5541ab40326a24d01d3d' },
        {
          url: '/whatsapp-bg.png',
          revision: '6264a93f7bf5f59d4512c2692a6f18e7',
        },
      ],
      { ignoreURLParametersMatching: [] }
    ),
    e.cleanupOutdatedCaches(),
    e.registerRoute(
      '/',
      new e.NetworkFirst({
        cacheName: 'start-url',
        plugins: [
          {
            cacheWillUpdate: async ({
              request: e,
              response: s,
              event: t,
              state: a,
            }) =>
              s && 'opaqueredirect' === s.type
                ? new Response(s.body, {
                    status: 200,
                    statusText: 'OK',
                    headers: s.headers,
                  })
                : s,
          },
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,
      new e.CacheFirst({
        cacheName: 'google-fonts-webfonts',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 4, maxAgeSeconds: 31536e3 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,
      new e.StaleWhileRevalidate({
        cacheName: 'google-fonts-stylesheets',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 4, maxAgeSeconds: 604800 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,
      new e.StaleWhileRevalidate({
        cacheName: 'static-font-assets',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 4, maxAgeSeconds: 604800 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,
      new e.StaleWhileRevalidate({
        cacheName: 'static-image-assets',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 64, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\/_next\/image\?url=.+$/i,
      new e.StaleWhileRevalidate({
        cacheName: 'next-image',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 64, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\.(?:mp3|wav|ogg)$/i,
      new e.CacheFirst({
        cacheName: 'static-audio-assets',
        plugins: [
          new e.RangeRequestsPlugin(),
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\.(?:mp4)$/i,
      new e.CacheFirst({
        cacheName: 'static-video-assets',
        plugins: [
          new e.RangeRequestsPlugin(),
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\.(?:js)$/i,
      new e.StaleWhileRevalidate({
        cacheName: 'static-js-assets',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\.(?:css|less)$/i,
      new e.StaleWhileRevalidate({
        cacheName: 'static-style-assets',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\/_next\/data\/.+\/.+\.json$/i,
      new e.StaleWhileRevalidate({
        cacheName: 'next-data',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\.(?:json|xml|csv)$/i,
      new e.NetworkFirst({
        cacheName: 'static-data-assets',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      ({ url: e }) => {
        if (!(self.origin === e.origin)) return !1;
        const s = e.pathname;
        return !s.startsWith('/api/auth/') && !!s.startsWith('/api/');
      },
      new e.NetworkFirst({
        cacheName: 'apis',
        networkTimeoutSeconds: 10,
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 16, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      ({ url: e }) => {
        if (!(self.origin === e.origin)) return !1;
        return !e.pathname.startsWith('/api/');
      },
      new e.NetworkFirst({
        cacheName: 'others',
        networkTimeoutSeconds: 10,
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      ({ url: e }) => !(self.origin === e.origin),
      new e.NetworkFirst({
        cacheName: 'cross-origin',
        networkTimeoutSeconds: 10,
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 3600 }),
        ],
      }),
      'GET'
    );
});
