import useSWR from 'swr';
import { useAuthSWR } from '../useAuthSWR';

export const GetAllMenu = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/cafeteria/menu/list?${qs.toString()}`
  );

  return {
    menu: data,
    menuLoading: isLoading,
    mutate: mutate,
  };
};

export const GetAllMenuCat = () => {
  const { data, isLoading } = useAuthSWR(`/cafeteria/menu-category/list`);

  return {
    menuCategory: data,
    menuCategoryLoading: isLoading,
  };
};





//Orders

export const GetOrderStats = () => {
  const { data, isLoading } = useAuthSWR(`/cafeteria/orders/statistics`);

  return {
    orderStats: data,
    orderStatsLoading: isLoading,
  };
};

export const GetAllOrders = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/cafeteria/orders/list?${qs.toString()}`
  );

  return {
    orders: data,
    orderLoading: isLoading,
    mutate: mutate,
  };
};