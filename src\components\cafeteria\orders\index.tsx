'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { GetOrderStats, GetAllOrders } from '@/api/cafeteria/menu';
import { currencyFormat } from '@/lib/utils';
import DateRangeFilter from '@/components/common/date-range-filter';
import dayjs from 'dayjs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import {
  ShoppingCart,
  Search,
  Eye,
  CheckCircle,
  XCircle,
  Printer,
} from 'lucide-react';
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';

interface OrdersManagementProps {
  orderType: 'general' | 'staff' | 'special';
}

export default function OrdersManagement({ orderType }: OrdersManagementProps) {
  const {orderStats, orderStatsLoading} = GetOrderStats();
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);  const stats = orderStats?.data

  console.log(orderStats)

  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    queryParam,
    setQueryParam,
  } = useSearchAndPagination({ initialPageSize: 15 });

    useEffect(() => {
      let params = [];
  
      if (debouncedSearchTerm) {
        params.push(`search=${debouncedSearchTerm}`);
      }
  
      if (startDate) {
        const formattedStartDate = dayjs(startDate).format('YYYY-MM-DD');
        params.push(`startDate=${formattedStartDate}`);
      }
  
      if (endDate) {
        const formattedEndDate = dayjs(endDate).format('YYYY-MM-DD');
        params.push(`endDate=${formattedEndDate}`);
      }
  
      setQueryParam(params.join('&'));
    }, [debouncedSearchTerm, startDate, endDate, setQueryParam]);

  const { orders, orderLoading, mutate } = GetAllOrders(
     `?page=${currentPage}&limit=${pageSize}&${queryParam}`
  );
  const orderData = orders?.data?.orders;
  const totalPages = orders?.data?.totalPages ?? 0;

  console.log(orderData)

    const handleDateRangeChange = (
    start: Date | undefined,
    end: Date | undefined
  ) => {
    setStartDate(start);
    setEndDate(end);
  };


  return (
    <div className="space-y-4">
          <div className="flex flex-wrap gap-3 items-center">
            <DateRangeFilter
              onDateRangeChange={handleDateRangeChange}
              className="w-[250px]"
            />
            <div className="relative w-64">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Search className="w-4 h-4 text-gray-500" />
              </div>
              <Input
                type="text"
                placeholder="Search ..."
                className="pl-10 pr-4 py-2 w-full"
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>
          </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Orders
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalOrders}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {`Today's Orders`}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-amber-500">
              {stats?.dailyOrders}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {`Today's Revenue`}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">
              {currencyFormat(stats?.dailyRevenue)}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>S/N</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Order No</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Sales Type</TableHead>
              <TableHead>Payment</TableHead>
              <TableHead>Total Amount</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {orderData.length > 0 ? (
              orderData?.map((order: any, index: number) => (
                <TableRow key={index}>
                  <TableCell>  {currentPage === 1
                    ? index + 1
                    : (currentPage - 1) * pageSize + (index + 1)}</TableCell>
                  <TableCell>{dayjs(order.createdAt).format('YYYY-MM-DD')}</TableCell>
                  <TableCell>{order.orderNumber}</TableCell>
                  <TableCell>{order.orderType}</TableCell>
                  <TableCell>{order?.saleType.toUpperCase()}</TableCell>
                  <TableCell>{order.paymentType}</TableCell>
                  <TableCell>{currencyFormat(order.totalAmount)}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button variant="ghost" size="icon">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-4">
                  No orders found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
