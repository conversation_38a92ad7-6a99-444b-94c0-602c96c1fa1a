'use client';

import { <PERSON><PERSON>, Chevron<PERSON>eft, ChevronRight } from 'lucide-react';
import { LeftSidebarData, LeftSidebarFooterData } from './data';
import { useState } from 'react';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import NavItem from './navItem';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import Logo from '@/assets/logo.png';
import Icon from '@/assets/icon.png';

// Helper function to map sidebar items to their corresponding permissions
function mapItemToPermission(itemLabel: string): string | null {
  const permissionMap: Record<string, string | null> = {
    Dashboard: PERMISSIONS.DASHBOARD_VIEW,
    Packages: PERMISSIONS.PACKAGE_VIEW,
    Feedbacks: PERMISSIONS.FEEDBACK_VIEW,
    Referrals: PERMISSIONS.REFERRAL_VIEW,
    Reporting: PERMISSIONS.INCIDENT_VIEW,
    Discounts: PERMISSIONS.REWARD_VIEW,
    Transactions: PERMISSIONS.TRANSACTION_VIEW,
    Staffs: PERMISSIONS.STAFF_VIEW,
    Rewards: PERMISSIONS.REWARD_VIEW,
    Locations: PERMISSIONS.LOCATION_EDIT,
    Forum: PERMISSIONS.FORUM_VIEW,
    'Innovation Hub': PERMISSIONS.DASHBOARD_VIEW,
    'Process Dictionary': PERMISSIONS.DASHBOARD_VIEW,
  };

  return permissionMap[itemLabel] || null;
}

export default function Sidebar() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const pathname = usePathname();

  // Filter sidebar items based on permissions
  const filteredSidebarData = LeftSidebarData.map((section) => ({
    ...section,
    children: section.children.filter((item) => {
      // Map sidebar item to corresponding permission
      const requiredPermission = mapItemToPermission(item.label);
      return requiredPermission ? hasPermission(requiredPermission) : true;
    }),
  }));

  // Filter out sections with no children
  const nonEmptySections = filteredSidebarData.filter(
    (section) => section.children.length > 0
  );

  // Filter footer items based on permissions
  const filteredFooterData = LeftSidebarFooterData.filter((item) => {
    const requiredPermission = mapItemToPermission(item.label);
    return requiredPermission ? hasPermission(requiredPermission) : true;
  });

  return (
    <>
      <button
        type="button"
        className="lg:hidden fixed top-4 left-4 z-30 p-2 rounded-lg bg-white dark:bg-[#0F0F12] shadow-md"
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
      >
        <Menu className="h-5 w-5 text-gray-600 dark:text-gray-300" />
      </button>
      <nav
        className={`
                fixed inset-y-0 left-0 z-40 bg-white dark:bg-[#0F0F12] transform transition-all duration-200 ease-in-out
                lg:translate-x-0 lg:static border-r border-gray-200 dark:border-[#1F1F23]
                ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'}
                ${isCollapsed ? 'lg:w-20' : 'lg:w-64'}
                w-64
            `}
      >
        <div className="h-full flex flex-col">
          <div className="h-16 px-6 flex items-center justify-between border-b border-gray-200 dark:border-[#1F1F23]">
            {isCollapsed ? (
              <Image
                src={Icon}
                alt="icon"
                width={32}
                height={32}
                className="flex-shrink-0"
                unoptimized
              />
            ) : (
              <Image
                src={Logo}
                alt="logo"
                width={120}
                height={120}
                className="flex-shrink-0"
                unoptimized
              />
            )}
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="hidden lg:flex p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              {isCollapsed ? (
                <ChevronRight className="h-4 w-4 text-gray-600 dark:text-gray-300" />
              ) : (
                <ChevronLeft className="h-4 w-4 text-gray-600 dark:text-gray-300" />
              )}
            </button>
          </div>

          <div className="flex-1 overflow-y-auto py-4 px-4">
            <div className="space-y-6">
              {nonEmptySections.map((section, sectionIndex) => (
                <div key={sectionIndex}>
                  {!isCollapsed && (
                    <div className="px-3 mb-2 text-xs font-semibold uppercase tracking-wider text-gray-500 dark:text-gray-400">
                      {section.name}
                    </div>
                  )}
                  <div className="space-y-1">
                    {section.children.map((item, index) => {
                      const isActive = pathname.startsWith(item.url);
                      return (
                        <NavItem
                          key={index}
                          url={item.url}
                          icon={item.icon}
                          isActive={isActive}
                          collapsed={isCollapsed}
                          onClick={() => {
                            if (isMobileMenuOpen) {
                              setIsMobileMenuOpen(false);
                            }
                          }}
                        >
                          {item.label}
                        </NavItem>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Fixed Footer */}
          <div className="border-t border-gray-200 dark:border-[#1F1F23] p-4">
            <div className="space-y-1">
              {filteredFooterData.map((item, index) => {
                const isActive = pathname.startsWith(item.url);
                return (
                  <NavItem
                    key={index}
                    url={item.url}
                    icon={item.icon}
                    isActive={isActive}
                    collapsed={isCollapsed}
                    onClick={() => {
                      if (isMobileMenuOpen) {
                        setIsMobileMenuOpen(false);
                      }
                    }}
                  >
                    {item.label}
                  </NavItem>
                );
              })}
            </div>
            {!isCollapsed && (
              <div className=" pt-2 border-t border-gray-200 dark:border-[#1F1F23]">
                <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                  © {new Date().getFullYear()} Cedarcrest Hospitals Innovations
                </p>
              </div>
            )}
          </div>
        </div>
      </nav>

      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black/70 z-30 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
    </>
  );
}
