'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { BookOpen, Construction, Sparkles } from 'lucide-react';

export default function ProcessDictionaryContent() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="p-2 bg-primary/10 rounded-lg">
          <BookOpen className="h-6 w-6 text-primary" />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Process Dictionary
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Comprehensive guide to all processes and procedures
          </p>
        </div>
      </div>

      {/* Coming Soon Card */}
      <Card className="max-w-2xl mx-auto">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="relative">
              <div className="p-4 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full">
                <Construction className="h-12 w-12 text-primary" />
              </div>
              <div className="absolute -top-1 -right-1">
                <Sparkles className="h-6 w-6 text-yellow-500 animate-pulse" />
              </div>
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
            Feature Coming Soon!
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-lg text-gray-600 dark:text-gray-400">
            Currently in Development
          </p>
          <div className="bg-gradient-to-r from-primary/5 to-primary/10 rounded-lg p-6">
            <p className="text-gray-700 dark:text-gray-300">
              We're building a comprehensive process dictionary that will serve as your 
              go-to resource for understanding workflows, procedures, and best practices. 
              This feature will provide detailed documentation and step-by-step guides 
              for all organizational processes.
            </p>
          </div>
          <div className="flex justify-center">
            <div className="flex space-x-2">
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
