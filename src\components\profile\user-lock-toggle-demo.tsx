'use client';

import React, { useState } from 'react';
import { Lock, Unlock } from 'lucide-react';
import { Switch } from '../ui/switch';
import { Button } from '../ui/button';
import { toast } from 'sonner';
import { UserProps } from './types';

interface UserLockToggleDemoProps {
  user: UserProps;
  onToggle?: (locked: boolean) => void;
}

/**
 * Demo component to showcase the user lock toggle functionality
 * This demonstrates how the Switch component can be used to toggle user.locked status
 */
export default function UserLockToggleDemo({
  user,
  onToggle,
}: UserLockToggleDemoProps) {
  const [isToggling, setIsToggling] = useState(false);
  const [currentUser, setCurrentUser] = useState(user);

  const handleToggleLocked = async () => {
    try {
      setIsToggling(true);

      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Toggle the locked status
      const newLockedStatus = !currentUser.locked;
      const updatedUser = { ...currentUser, locked: newLockedStatus };

      setCurrentUser(updatedUser);

      toast.success(
        `User ${newLockedStatus ? 'locked' : 'unlocked'} successfully`
      );

      // Call the optional callback
      if (onToggle) {
        onToggle(newLockedStatus);
      }
    } catch (error) {
      toast.error('Failed to update user status');
    } finally {
      setIsToggling(false);
    }
  };

  const resetDemo = () => {
    setCurrentUser(user);
    toast.info('Demo reset to original state');
  };

  return (
    <div className="max-w-md mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
        User Lock Toggle Demo
      </h3>

      {/* User Info */}
      <div className="mb-4">
        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
          {currentUser.fullName}
        </h4>
        <p className="text-xs text-gray-500 dark:text-gray-400">
          {currentUser.email}
        </p>
      </div>

      {/* Lock Status Toggle */}
      <div className="flex items-center justify-between rounded-lg border p-3 shadow-sm mb-4">
        <div className="flex items-center gap-2">
          {currentUser.locked ? (
            <Lock className="w-4 h-4 text-red-500" />
          ) : (
            <Unlock className="w-4 h-4 text-green-500" />
          )}
          <div className="space-y-0.5">
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Account {currentUser.locked ? 'Locked' : 'Unlocked'}
            </span>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {currentUser.locked
                ? 'User cannot access the system'
                : 'User has full access to the system'}
            </p>
          </div>
        </div>
        <Switch
          checked={currentUser.locked}
          onCheckedChange={handleToggleLocked}
          disabled={isToggling}
          aria-label="Toggle user lock status"
        />
      </div>

      {/* Demo Controls */}
      <div className="flex gap-2">
        <Button
          onClick={resetDemo}
          variant="outline"
          size="sm"
          className="flex-1"
        >
          Reset Demo
        </Button>
        <Button
          onClick={handleToggleLocked}
          disabled={isToggling}
          size="sm"
          className="flex-1"
        >
          {isToggling ? 'Toggling...' : 'Toggle Lock'}
        </Button>
      </div>

      {/* Status Display */}
      <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <p className="text-xs text-gray-600 dark:text-gray-300">
          <strong>Current Status:</strong>{' '}
          {currentUser.locked ? 'LOCKED' : 'UNLOCKED'}
        </p>
        <p className="text-xs text-gray-600 dark:text-gray-300 mt-1">
          <strong>Staff ID:</strong> {currentUser.staffId}
        </p>
      </div>
    </div>
  );
}
