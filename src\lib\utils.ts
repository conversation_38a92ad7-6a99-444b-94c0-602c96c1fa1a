import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import storage from './storage';
import { accessTokenStore, staffAccessToken } from '@/store/accessToken';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const truncateTxt = (value: string, length: number) => {
  return value.length > length ? `${value.substring(0, length)}...` : value;
};

export const currencyFormat = (value: number | string): string => {
  const naira = '₦';
  const amount = typeof value === 'string' ? parseFloat(value) : value;

  if (amount >= 1_000_000) {
    const formatted = (amount / 1_000_000).toFixed(amount % 1_000_000 === 0 ? 0 : 1);
    return `${naira}${formatted} M`;
  } else if (amount >= 100_000) {
    const formatted = (amount / 1_000).toFixed(amount % 1_000 === 0 ? 0 : 1);
    return `${naira}${formatted} K`;
  } else {
    return `${naira}${new Intl.NumberFormat('en-US').format(amount)}`;
  }
};

export const numberFormat = (value: number | string) =>
  new Intl.NumberFormat('en-NG', {
    style: 'currency',
    currency: 'NGN',
  }).format(typeof value === 'string' ? parseFloat(value) : value);

export const Logout = () => {
  storage.remove(staffAccessToken);
  accessTokenStore.accessToken = undefined;
};

// export const isZero = (val: number) => Math.abs(val) < 0.0001;

interface FormatValue {
  (number: number): string | number;
}

export const formatValue: FormatValue = (number) => {
  if (number < 1e3) return number;
  if (number >= 1e3 && number < 1e6) return +(number / 1e3).toFixed(1) + 'K';
  if (number >= 1e6 && number < 1e9) return +(number / 1e6).toFixed(1) + 'M';
  if (number >= 1e9 && number < 1e12) return +(number / 1e9).toFixed(1) + 'B';
  if (number >= 1e12) return +(number / 1e12).toFixed(1) + 'T';
  return number;
};

export const getStatusColor = (status: string) => {
  switch (status) {
    case 'PENDING_REVIEW':
      return 'bg-yellow-100 text-yellow-800';
    case 'ACCEPTED':
      return 'bg-blue-100 text-blue-800';
    case 'IN_PROGRESS':
      return 'bg-purple-100 text-purple-800';
    case 'COMPLETED':
      return 'bg-green-100 text-green-800';
    case 'REJECTED':
      return 'bg-red-100 text-red-800';
    case 'CANCELLED':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const getUrgencyColor = (urgency: string) => {
  switch (urgency?.toLowerCase()) {
    case 'emergency':
      return 'bg-red-100 text-red-800';
    case 'urgent':
      return 'bg-orange-100 text-orange-800';
    case 'routine':
      return 'bg-green-100 text-green-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

type Role = {
  id: number;
  name: string;
  permissions: any[];
};

export function formatRoleNames(roles: Role[]): string {
  return roles
    .map((role) =>
      role.name
        .split('_')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')
    )
    .join(', ');
}

export function toTitleCase(str: string): string {
  if (!str) return '';

  return str
    .toLowerCase()
    .trim()
    .split(/\s+/) // split by one or more spaces
    .map((word: string) => {
      return word
        .split(/(-|')/) // keep hyphens and apostrophes in split
        .map((part: string) =>
          part.match(/[-']/) ? part : part.charAt(0).toUpperCase() + part.slice(1)
        )
        .join('');
    })
    .join(' ');
}

export function formatAmount(amount: number): string {
  const naira = '₦';

  if (amount >= 1_000_000) {
    return `${naira}${(amount / 1_000_000).toFixed(amount % 1_000_000 === 0 ? 0 : 1)} M`;
  } else if (amount >= 100_000) {
    return `${naira}${(amount / 1_000).toFixed(amount % 1_000 === 0 ? 0 : 1)} K`;
  } else {
    return `${naira}${amount}`;
  }
}
